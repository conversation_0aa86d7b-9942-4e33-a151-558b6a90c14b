{"name": "eslint-config-airbnb-typescript", "version": "17.1.0", "description": "Airbnb's ESLint config with TypeScript support", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://iamturns.com)", "homepage": "https://github.com/iamturns/eslint-config-airbnb-typescript", "repository": {"type": "git", "url": "https://github.com/iamturns/eslint-config-airbnb-typescript.git"}, "bugs": {"url": "https://github.com/iamturns/eslint-config-airbnb-typescript/issues"}, "scripts": {"pre-commit": "lint-staged", "format": "run-s format:package format:prettier format:eslint", "format:eslint": "eslint --fix ./ >/dev/null 2>&1 || true", "format:package": "prettier-package-json --write", "format:prettier": "prettier --write \"**/*.{js,json,md,yml}\" \".editorconfig\" \"LICENSE\"", "lint": "eslint ./", "prepare": "husky install", "validate": "npm run lint"}, "dependencies": {"eslint-config-airbnb-base": "^15.0.0"}, "peerDependencies": {"@typescript-eslint/eslint-plugin": "^5.13.0 || ^6.0.0", "@typescript-eslint/parser": "^5.0.0 || ^6.0.0", "eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.13.0 || ^6.0.0", "@typescript-eslint/parser": "^5.0.0 || ^6.0.0", "doctoc": "2.1.0", "eslint": "8.11.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-import": "2.25.4", "husky": "7.0.4", "lint-staged": "12.3.7", "npm-run-all": "4.1.5", "prettier": "2.6.0", "prettier-package-json": "2.6.3", "typescript": "4.6.2"}, "keywords": ["airbnb", "config", "es2015", "es2016", "es2017", "es2018", "eslint", "eslintconfig", "javascript", "styleguide", "typescript"]}