{"name": "eslint-config-airbnb", "version": "19.0.4", "description": "Airbnb's ESLint config, following our styleguide", "main": "index.js", "exports": {".": "./index.js", "./base": "./base.js", "./hooks": "./hooks.js", "./legacy": "./legacy.js", "./whitespace": "./whitespace.js", "./rules/react": "./rules/react.js", "./rules/react-a11y": "./rules/react-a11y.js", "./rules/react-hooks": "./rules/react-hooks.js", "./package.json": "./package.json"}, "scripts": {"prelint": "eclint check * rules/* test/*", "lint": "eslint .", "pretests-only": "node ./test/requires", "tests-only": "babel-tape-runner ./test/test-*.js", "prepublishOnly": "eslint-find-rules --unused && npm test && safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "link:eslint": "cd node_modules/eslint && npm link --production && cd -", "pretravis": "npm run link:eslint && cd ../eslint-config-airbnb-base && npm link --no-save eslint && npm install && npm link && cd - && npm link --no-save eslint-config-airbnb-base", "travis": "npm run --silent tests-only", "posttravis": "npm unlink --no-save eslint-config-airbnb-base eslint >/dev/null &"}, "repository": {"type": "git", "url": "https://github.com/airbnb/javascript"}, "keywords": ["eslint", "eslintconfig", "config", "airbnb", "javascript", "styleguide", "es2015", "es2016", "es2017", "es2018"], "author": "<PERSON> (https://twitter.com/@jitl)", "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/jitl"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON>", "url": "https://twitter.com/hshoff"}], "license": "MIT", "bugs": {"url": "https://github.com/airbnb/javascript/issues"}, "homepage": "https://github.com/airbnb/javascript", "dependencies": {"eslint-config-airbnb-base": "^15.0.0", "object.assign": "^4.1.2", "object.entries": "^1.1.5"}, "devDependencies": {"@babel/runtime": "^7.16.3", "babel-preset-airbnb": "^4.5.0", "babel-tape-runner": "^3.0.0", "eclint": "^2.8.1", "eslint": "^7.32.0 || ^8.2.0", "eslint-find-rules": "^4.0.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "in-publish": "^2.0.1", "react": ">= 0.13.0", "safe-publish-latest": "^2.0.0", "tape": "^5.3.2"}, "peerDependencies": {"eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0"}, "engines": {"node": "^10.12.0 || ^12.22.0 || ^14.17.0 || >=16.0.0"}}